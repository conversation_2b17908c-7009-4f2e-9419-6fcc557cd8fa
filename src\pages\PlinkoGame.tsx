/**
 * PlinkoGame Component
 * A Plinko-style betting game where users drop a ball through pegs to win multipliers.
 *
 * @component
 * @param {Object} props
 * @param {balance: number, onWin: function, onPurchase: function} props
 */

import { useState, useEffect, useRef } from 'react';
import {
  plinkoDrop,
  getPlinkoConfig,
  getPlinkoGameHistory,
} from '../api/plinkoService';
import { useQuery, useMutation, useInfiniteQuery } from '@tanstack/react-query';
import { CustomToast } from '../utils/validations/customeToast';
import { useAuth } from '../auth/AuthContext';
import { ChevronLeft, Coins } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { useCoins } from '../hooks/useCoinsQuery';
import winSound from '../Sounds/CryptoKing/win_sound.m4a';
import BackGroundSound from '../Sounds/Plinko/ping-pong-ball.mp3';
import SoundManager from '../components/soundManager/SoundManager';
import Transactions from '../components/Transactions';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from '../components/GameLoadingScreen/GameLoadingScreen';
import { IMAGES } from '../constant/image';
import { PzButton } from '../components/Shared/PzButton';
import { COLORS } from '../constant/theming';
import { PzErrorPopup } from '../components/Shared/PzErrorPopup';
import { DEFAULT_BET } from '../constant/numbers';
/**
 * Format a monetary amount to two decimal places.
 * @param {number|string} amount
 * @returns {string}
 */
const formatAmount = (amount: number | string) =>
  `${parseFloat(amount as string).toFixed(2)}`;

/**
 * Format a coin amount to no decimal places.
 * @param {number|string} amount
 * @returns {string}
 */
const formatCoins = (amount: number | string) => parseFloat(amount as string);

type PlinkoPageProps = {
  balance: number;
  onWin: (reward: { type: string; amount?: number }) => void;
  onPurchase: (cost: number) => void;
};

const BALL_BOUNCE_SHIFT = 20;

const PlinkoGame: React.FC<PlinkoPageProps> = ({
  onWin,
  onPurchase,
  balance,
}) => {
  const { accessToken } = useAuth();
  const { coins, refetchCoins } = useCoins();
  const navigate = useNavigate();
  const [showTransactionModal, setShowTransactionModal] = useState(false);

  // Canvas reference for drawing pegs, ball, and multipliers
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [didWin, setDidWin] = useState(false);
  const [soundOn, setSoundOn] = useState<boolean>(false);
  // Animation & game state
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [betAmount, setBetAmount] = useState<number | string>(DEFAULT_BET);
  const [multipliers, setMultipliers] = useState<number[]>([]);
  const [showFundsErrorPopup, setShowFundsErrorPopup] = useState(false);

  // Canvas dimensions and drawing constants
  const CANVAS_WIDTH = 600;
  const CANVAS_HEIGHT = 600;
  const PEG_RADIUS = 8;
  const BALL_RADIUS = 8;
  const BOUNCE_HEIGHT = 60; // pixels above peg to bounce

  // Scroll to top on mount
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Fetch Plinko configuration (multipliers, bet limits, etc.)
  const {
    data: config,
    isLoading: isConfigLoading,
    isError: isConfigError,
  } = useQuery({
    queryKey: ['plinkoConfig'],
    queryFn: getPlinkoConfig,
    staleTime: 1000 * 60 * 60,
    refetchOnWindowFocus: false,
  });

  // Update multipliers when config loads
  useEffect(() => {
    if (config?.multipliers) {
      setMultipliers(config.multipliers);
    }
  }, [config]);

  // Fetch game history with infinite scroll
  const {
    data: historyPages,
    refetch,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isHistoryLoading,
    isError: isHistoryError,
  } = useInfiniteQuery({
    queryKey: ['games'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error('Access token missing');

      return getPlinkoGameHistory(
        { page: pageParam, per_page: 10 },
        accessToken
      );
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Determine if more pages exist
      if (!lastPage?.total_pages || allPages.length >= lastPage.total_pages)
        return undefined;
      return allPages.length + 1;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    enabled: !!accessToken,
  });

  // Mutation to drop the ball and place bet
  const dropBallMutation = useMutation({
    mutationFn: (data: { amount: number; currency: string }) =>
      plinkoDrop(data, accessToken!),
    onSuccess: () => CustomToast('success', `Bet placed successfully!`),
    onError: () => CustomToast('error', 'Failed to place bet'),
  });

  /**
   * Generate the array of peg positions for the board.
   * @returns {{ x: number; y: number }[]}
   */
  const generatePegs = () => {
    const pegs: { x: number; y: number }[] = [];
    const rows = 12;
    const spacing = 48;
    for (let row = 0; row < rows; row++) {
      const numInRow = row + 1;
      const rowWidth = numInRow * spacing;
      const startX = (CANVAS_WIDTH - rowWidth) / 2 + spacing / 2;
      for (let col = 0; col < numInRow; col++) {
        pegs.push({ x: startX + col * spacing, y: 40 + row * spacing });
      }
    }
    return pegs;
  };

  // Static peg positions
  const pegs = generatePegs();

  /**
   * Initiate the ball drop animation and handle result.
   */
  const dropBall = () => {
    if (coins < Number(betAmount)) {
      setShowFundsErrorPopup(true);
      return;
    }
    if (isAnimating) return;

    dropBallMutation.mutate(
      { amount: Number(betAmount), currency: 'P' },
      {
        onSuccess: (data) => {
          onPurchase(Number(betAmount));
          setIsAnimating(true);
          const { drop_path, win_amount, final_position } = data;
          if (!drop_path?.length) {
            setIsAnimating(false);
            return;
          }

          const canvas = canvasRef.current!;
          const ctx = canvas.getContext('2d')!;
          let pathIndex = 0;
          let frame = 0;
          const framesPerMove = 30;
          let finalAnimating = false;
          let finalFrame = 0;
          const finalFrames = 30;
          let finalStart = { x: 0, y: 0 };
          let finalTarget = { x: 0, y: 0 };

          /**
           * Get peg position by drop_path index.
           * @param idx
           */
          const getPos = (idx: number) => {
            const { Row, Col } = drop_path[idx];
            const pegIndex = (Row * (Row + 1)) / 2 + Col;
            return pegs[pegIndex];
          };

          /**
           * Bezier interpolation for bounce effect.
           */
          const bezier = (t: number, p0: number, p1: number, p2: number) =>
            (1 - t) ** 2 * p0 + 2 * (1 - t) * t * p1 + t ** 2 * p2;

          /**
           * Draw the ball at (x, y).
           */
          const drawBall = (x: number, y: number) => {
            setSoundOn(true);
            ctx.beginPath();
            ctx.arc(x, y, BALL_RADIUS, 0, Math.PI * 2);
            ctx.fillStyle = '#FFD700';
            ctx.fill();
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.stroke();
          };

          /**
           * Core animation loop.
           */
          const animate = () => {
            ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            drawPegs(ctx);
            drawMultipliers(ctx);

            // Final drop animation
            if (pathIndex >= drop_path.length - 1) {
              if (!finalAnimating && final_position !== undefined) {
                finalAnimating = true;
                finalStart = getPos(drop_path.length - 1);
                const zoneW = CANVAS_WIDTH / multipliers.length;
                finalTarget = {
                  x: final_position * zoneW + zoneW / 2,
                  y: CANVAS_HEIGHT - 25,
                };
              }
              if (finalAnimating) {
                const t = finalFrame / finalFrames;
                const ease = t * t;
                const x = finalStart.x + (finalTarget.x - finalStart.x) * ease;
                const y = finalStart.y + (finalTarget.y - finalStart.y) * ease;
                if (finalFrame < finalFrames) drawBall(x, y);
                if (++finalFrame <= finalFrames) {
                  requestAnimationFrame(animate);
                } else {
                  setTimeout(() => {
                    setIsAnimating(false);
                    setSoundOn(false);
                    setDidWin(true);
                    CustomToast('success', `You won: ${win_amount}!`);
                    refetch();
                    refetchCoins();
                    onWin({ type: 'coins' });
                  }, 500);
                }
              }
              return;
            }

            // Calculate bounce path between pegs
            const start = getPos(pathIndex);
            const end = getPos(pathIndex + 1);
            const peg = getPos(pathIndex + 1);
            const dx = end.x - start.x;
            const dy = end.y - start.y;
            const dist = Math.hypot(dx, dy);
            const ux = dx / dist;
            const uy = dy / dist;
            const touchX = peg.x - ux * 30;
            const touchY = peg.y - uy * 30;
            const control = { x: touchX, y: touchY - BOUNCE_HEIGHT };
            const t = frame / framesPerMove;
            const x = bezier(t, start.x, control.x, end.x);
            const y = bezier(
              t,
              start.y - BALL_BOUNCE_SHIFT,
              control.y - BALL_BOUNCE_SHIFT,
              end.y - BALL_BOUNCE_SHIFT
            );

            drawBall(x, y);
            frame++;
            if (frame > framesPerMove) {
              frame = 0;
              pathIndex++;
            }
            requestAnimationFrame(animate);
          };

          // Start animation
          requestAnimationFrame(animate);
        },
      }
    );
  };

  /**
   * Draw all pegs on the canvas.
   * @param ctx CanvasRenderingContext2D
   */
  const drawPegs = (ctx: CanvasRenderingContext2D) => {
    pegs.forEach((peg) => {
      ctx.shadowColor = 'rgba(0,0,0,0.4)';
      ctx.shadowBlur = 6;
      ctx.shadowOffsetY = 9;
      ctx.beginPath();
      ctx.arc(peg.x, peg.y, PEG_RADIUS, 0, Math.PI * 2);
      ctx.fillStyle = 'white';
      ctx.fill();
    });
  };

  /**
   * Draw multiplier zones at bottom of canvas.
   * @param ctx CanvasRenderingContext2D
   */
  const drawMultipliers = (ctx: CanvasRenderingContext2D) => {
    const zoneW = CANVAS_WIDTH / multipliers.length;

    const grad = ctx.createLinearGradient(0, 0, CANVAS_WIDTH, 0);
    grad.addColorStop(0, '#220824');
    grad.addColorStop(0.5, '#600967');
    grad.addColorStop(1, '#300933');

    // Fill rectangle with gradient
    ctx.fillStyle = grad;
    ctx.fillRect(0, CANVAS_HEIGHT - 50, CANVAS_WIDTH, 50);

    // The custom sequence is: 10x, 0.6x, 1.5x, 0.2x, 30x, 0.8x, 3x, 0.4x, 1x

    multipliers.forEach((val, i) => {
      ctx.fillStyle = 'rgba(255,255,255,0.9)';
      ctx.font = '16px Poppins';
      ctx.textAlign = 'center';
      ctx.fillText(`${val}x`, i * zoneW + zoneW / 2, CANVAS_HEIGHT - 20);
    });
  };

  // Initial draw when multipliers load
  useEffect(() => {
    const ctx = canvasRef.current?.getContext('2d');
    if (ctx) {
      drawPegs(ctx);
      setDidWin(false);
      drawMultipliers(ctx);
    }
  }, [multipliers]);
  useEffect(() => {
    if (didWin) {
      // Reset didWin when a new game starts
      return () => {
        setDidWin(false);
      };
    }
  }, [isAnimating]);

  useEffect(() => {
    isConfigError && CustomToast('error', 'Failed to fetch configuration');
    isHistoryError && CustomToast('error', 'Failed to fetch history');
  }, [isConfigError, isHistoryError]);

  const onClose = () => {
    setShowTransactionModal(false);
  };

  const { isGameDataLoading, visibility } = useGameDataLoading(
    !isConfigLoading,
    !isHistoryLoading,
    isFinite(coins)
  );

  const goToHomePage = () => navigate('/');

  return (
    <>
      <SoundManager
        sounds={{
          background: BackGroundSound,
          win: winSound,
        }}
        // loop the background while the game is running:
        backgroundKey={soundOn ? 'background' : null}
        // play one of these exactly once when it changes:
        playKey={didWin ? 'win' : undefined}
        volumes={{
          background: 0.9,
          win: 1.0,
        }}
      />
      {isGameDataLoading && <GameLoadingScreen />}
      <div
        className="min-h-screen w-full text-text-primary relative"
        style={{
          visibility,
        }}
      >
        <div className="max-w-7xl mx-auto relative">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={goToHomePage}
              className="text-white/60 hover:text-white transition-colors"
            >
              <ChevronLeft size={24} />
            </button>
            <h1 className="text-2xl font-[Anton]">Plinko</h1>
          </div>
        </div>

        {/* Background & Canvas */}
        <div className="relative mb-2 bg-cover bg-center">
          <img
            src={IMAGES.PLINKO_FIELD_BG}
            alt="Plinko Background"
            className="block w-full h-auto rounded-2xl"
            style={{ objectFit: 'cover', filter: 'brightness(0.7)' }}
          />
          <canvas
            ref={canvasRef}
            width={CANVAS_WIDTH}
            height={CANVAS_HEIGHT}
            className="absolute top-0 left-0 w-full h-full rounded-2xl"
          />
        </div>

        <PzButton
          text={isAnimating ? 'Ball Dropping...' : 'Drop Ball'}
          onClick={dropBall}
          isDisabled={isAnimating}
          className="mt-4"
        />

        {/* Bet Controls */}
        <div className="bg-[#131313] font-[Poppins] backdrop-blur-sm rounded-2xl p-4 mb-6 mt-6">
          <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div className="flex flex-1 gap-2">
              {/* Min Bet */}
              <div className="flex-1">
                <label className="text-sm font-[Poppins]">Min</label>
                <button
                  disabled={isAnimating}
                  onClick={() => setBetAmount(10)}
                  className="w-full text-sm text-left h-[3rem] bg-[#252525] border border-white/10 rounded-lg px-4 py-2 hover:bg-white/10 transition-colors"
                >
                  {10}
                </button>
              </div>
              {/* Max Bet */}
              <div className="flex-1">
                <label className="text-sm font-[Poppins]">Max</label>
                <button
                  disabled={isAnimating}
                  onClick={() =>
                    setBetAmount(parseFloat(config?.bet_limits?.max || '100'))
                  }
                  className="w-full text-sm text-left h-[3rem] bg-[#252525] border border-white/10 rounded-lg px-4 py-2 hover:bg-white/10 transition-colors"
                >
                  {config?.bet_limits?.max}
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-6">
            <label className="block text-sm font-[Poppins] mt-2">
              Bet Amount
            </label>
            <label className="block text-sm mt-2">Balance</label>
            <div className="relative">
              <input
                type="number"
                disabled={isAnimating}
                min={config?.bet_limits?.min}
                max={config?.bet_limits?.max}
                value={betAmount}
                onChange={(e) => {
                  const val = e.target.value || '';
                  setBetAmount(
                    val === ''
                      ? val
                      : Math.min(config?.bet_limits?.max, Number(val))
                  );
                }}
                className={`w-full h-[3rem] bg-[#252525] focus:border-[${COLORS.primary}] text-sm border border-white/10 rounded-lg px-4 py-2 focus:outline-none`}
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"></div>
            </div>
            <div className="flex bg-[#252525] text-sm items-center gap-2 px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white">
              <Coins
                size={16}
                color={COLORS.primary}
                className="text-yellow-400"
              />
              {`${formatCoins(balance)}`}
            </div>
          </div>
        </div>

        {/* Game History */}
        <div className="bg-[#131313] backdrop-blur-sm rounded-2xl p-6">
          <h2 className="font-[Anton] mb-4 flex items-center gap-2">
            Game History
          </h2>
          <div className="space-y-4">
            {historyPages?.pages
              ?.flatMap((page) => page.games)
              .map(
                (game: {
                  id: string;
                  win_amount: string;
                  multiplier: string;
                  bet_amount: string;
                  timestamp: string;
                }) => {
                  const wonAmt = parseFloat(game.win_amount);
                  const lost = wonAmt <= 0;
                  return (
                    <div
                      key={game.id}
                      className={`p-4 rounded-lg font-[Poppins] ${
                        lost ? 'bg-red-500/10' : 'bg-green-500/10'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span
                          className={lost ? 'text-red-400' : 'text-green-400'}
                        >
                          {`${parseFloat(game.multiplier).toFixed(2)}x`}
                        </span>
                        <span
                          className={lost ? 'text-red-400' : 'text-green-400'}
                        >
                          {lost
                            ? `-${formatAmount(game.bet_amount)}`
                            : `+${formatAmount(game.win_amount)}`}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm text-white/60">
                        <span>Bet: {formatAmount(game.bet_amount)}</span>
                        <span>{dayjs(game.timestamp).format('hh:mm A')}</span>
                      </div>
                    </div>
                  );
                }
              )}

            {hasNextPage && (
              <PzButton
                text="View All"
                onClick={() => {
                  setShowTransactionModal(true);
                }}
                isDisabled={isFetchingNextPage}
              />
            )}
            {historyPages?.pages[0]?.games.length === 0 && (
              <div className="text-center text-white/40 py-4">
                No game history yet
              </div>
            )}
          </div>
          {/* TODO: Insert your “Load More” button here, hooked up to fetchNextPage */}
        </div>
        {showTransactionModal && <Transactions onClose={onClose} />}
        {showFundsErrorPopup && (
          <PzErrorPopup
            setShowFundsErrorPopup={setShowFundsErrorPopup}
            cost={Number(betAmount)}
          />
        )}
      </div>
    </>
  );
};

export default PlinkoGame;
