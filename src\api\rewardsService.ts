import axios from 'axios'
import { RewardGameType } from '../types/Rewards';


export interface RewardClaimPayload {
  user_id: string;
  game: RewardGameType;
  position: number;
  transaction_id?: string;
  wallet_id?: string;
}

const API_BASE_URL = import.meta.env?.VITE_REWARD_BASE_URL

export const postRewardClaim = async (payload: RewardClaimPayload): Promise<void> => {
  try {
    const finalPayload = {
      ...payload,
      transaction_id: payload.transaction_id || 'tx123456789',
      wallet_id: payload.wallet_id || 'wallet_abc',
      source: 'android_app', // note: use underscore to match your expected format
    };

    await axios.post(`${API_BASE_URL}/reward-claim`, finalPayload, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': import.meta.env?.VITE_REWARD_API_KEY || '',
      },
    });
  } catch (error: any) {
    console.error('Failed to post reward claim:', error.response?.data || error.message);
    throw error;
  }
};

