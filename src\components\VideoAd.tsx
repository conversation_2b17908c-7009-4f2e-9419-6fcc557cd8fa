import React, { useState, useEffect, useRef } from 'react';
import { X, CheckCircle } from 'lucide-react';
import { postRewardClaim } from '../api/rewardsService';
import { useAuth } from '../auth/AuthContext';
import { RewardGameType } from '../types/Rewards';
import AdService from '../api/adService';
import { toast } from 'react-toastify';

interface VideoAdProps {
  onClose: () => void;
  onComplete?: () => void;
}

const VideoAd: React.FC<VideoAdProps> = ({ onClose, onComplete }) => {
  // UI state
  const [progress, setProgress] = useState(0);
  const [remainingTime, setRemainingTime] = useState(20);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [showReward, setShowReward] = useState(false);
  const rewardTimeoutRef = useRef<number>();

  // Detect WebView / Native bridge
  const isNative = typeof window.nativeHandler !== 'undefined';

  // user profile for web reward post
  const { userProfile } = useAuth();


  // —— WEB mode: when progress hits 100% ——
  useEffect(() => {
    if (isNative) return;               // skip in native
    if (progress < 100) return;

    const handleReward = async () => {
      setShowReward(true);

      // POST to your reward API
      try {
        if (userProfile?.user_id) {
          await postRewardClaim({
            user_id: userProfile.user_id,
            game: RewardGameType.REWARDED_AD,
            position: 1,
          });
        } else {
          console.error("UserId unknown")
        }
      } catch (error) {
        console.error('Reward claim failed:', error);
      }

      onComplete?.();

      rewardTimeoutRef.current = window.setTimeout(() => {
        onClose();
      }, 2000);
    };

    handleReward();

    return () => {
      if (rewardTimeoutRef.current) {
        clearTimeout(rewardTimeoutRef.current);
      }
    };
  }, [progress, isNative, onComplete, onClose, postRewardClaim, userProfile]);

  // —— WEB mode: video progress tracking ——
  const videoRef = useRef<HTMLVideoElement>(null);
  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    const current = videoRef.current.currentTime;
    const maxDuration = 20;
    const remaining = Math.max(maxDuration - current, 0);
    const pct = (current / maxDuration) * 100;

    setRemainingTime(Math.ceil(remaining));
    setProgress(Math.min(pct, 100));

    if (current >= maxDuration) {
      videoRef.current.pause();
      setProgress(100);
    }
  };
  const handleVideoLoad = () => {
    setIsVideoLoaded(true);
    videoRef.current?.play().catch(console.error);
  };
 
  useEffect(() => {
    if (!isNative) return;

    AdService.showRewardedAd({ userId: userProfile?.user_id ?? 'unknown' });

    const onNative = (action: string, data?: any) => {
      if (action === 'rewardAdClaimed') {
        setShowReward(true);
        onComplete?.();
        rewardTimeoutRef.current = window.setTimeout(onClose, 2000);
      }  else if (action === 'rewardAdClaimFailed') {
        toast.error('Reward Ad failed ' + (data?.error ?? ""));
        onClose()
      } else if (action === 'rewardClaimSkipped') {
        toast.error('Reward Ad skipped - no reward granted');
        onClose()
      }
    };
    AdService.listen(onNative);

    // cleanup on unmount
    return () => {
      AdService.cleanup();
      if (rewardTimeoutRef.current) clearTimeout(rewardTimeoutRef.current);
    };
  }, [isNative, onComplete, onClose, userProfile]);

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">
            {isNative ? 'Loading Reward…' : 'Watch Ad to Earn Bucks'}
          </h2>
          <button onClick={onClose} className="text-white/60 hover:text-white transition-colors">
            <X size={24} />
          </button>
        </div>

        {/* WEB video */}
        {!isNative && (
          <div className="relative">
            <div className="aspect-video w-full rounded-xl overflow-hidden bg-black">
              {!isVideoLoaded && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-pulse text-white/60">Loading video...</div>
                </div>
              )}
              <video
                ref={videoRef}
                muted
                playsInline
                onLoadedData={handleVideoLoad}
                onTimeUpdate={handleTimeUpdate}
                className="w-full h-full object-cover"
                src="https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
              />
            </div>
            <div className="bottom-0 left-0 right-0 h-1 bg-white/20 mt-4">
              <div
                className="h-full bg-yellow-400 transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        {/* COMMON reward UI */}
        {showReward && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
            <div className="text-center animate-fade-in">
              <CheckCircle size={48} className="text-green-400 mx-auto mb-3" />
              <p className="text-xl font-bold text-white">50 Bucks Earned! 🎉</p>
            </div>
          </div>
        )}

        {/* WEB footer stats */}
        {!isNative && !showReward && (
          <div className="mt-6 flex justify-between items-center">
            <p className="text-white/60 text-sm">Watch progress: {Math.round(progress)}%</p>
            <p className="text-sm font-medium text-yellow-400">⏱ {remainingTime}s remaining</p>
            <p className="text-sm font-medium text-yellow-400">Reward: 50 Bucks</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoAd;
