import axios from 'axios';
import {
  ADD_MEMBER_IN_SQUAD,
  DELETE_MEMBER,
  DELETE_MEMBER_ALL,
  DELETE_SQUAD,
  GET_LEVEL_PROGRESS_OF_SQUAD,
  GET_OUR_SQUAD,
  GET_OUR_SQUAD_BY_ID,
  GET_SQUAD,
  JOIN_MEMBER,
  LEAVE_SQUAD,
  RANKING_TOURNAMENT,
  SEARCH_SQUAD,
  SQUAD_CREATE,
  TOTAL_EARN_OF_SQUAD
} from './auth';

// ================== Types ==================
type Squad = {
  created_at: string;
  handle: string;
  type: string;
};

type squadSearch = {
  name: string;
};

type MemberAddTypes = {
  phone: string;
  squad_id: string;
};

type GetSquadByID = {
  page: number;
  per_page: number;
  squad_id: string;
};

type RankingTournament = {
  page: number;
  per_page: number;
};

type leaveSquad = {
  squad_id: string;
};

// ================== API Functions ==================

/**
 * Delete a specific member from squad by member ID
 */
export const deleteMember = async (id: string, token: string) => {
  const response = await axios.delete(`${DELETE_MEMBER}${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    }
  });
  return response;
};

/**
 * Delete all members from a squad
 */
export const deleteMemberAll = async (id: string, token: string) => {
  const response = await axios.delete(DELETE_MEMBER_ALL, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: { id },
  });
  return response;
};

/**
 * Delete a squad by its ID
 */
export const deleteSquad = async (id: string, token: string) => {
  const response = await axios.delete(DELETE_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: { id },
  });
  return response;
};

/**
 * Leave a squad
 */
export const levelSquad = async (data: leaveSquad, token: string) => {
  const response = await axios.delete(LEAVE_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      squad_id: data.squad_id
    }
  });
  return response;
};

/**
 * Get all squads the user is part of
 */
export const squadMembers = async (token: string) => {
  const response = await axios.get(GET_OUR_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    }
  });
  return response.data;
};

/**
 * Get squad details by ID with pagination
 */
export const getSquadByID = async (data: GetSquadByID, token: string) => {
  const response = await axios.get(GET_OUR_SQUAD_BY_ID, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      per_page: data.per_page,
      squad_id: data.squad_id
    }
  });
  return response.data;
};

/**
 * Get total earnings of a squad
 */
export const getTotalErans = async (squad_id: string, token: string) => {
  const response = await axios.get(TOTAL_EARN_OF_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: { squad_id }
  });
  return response.data;
};

/**
 * Get all available squads
 */
export const getSquad = async (token: string) => {
  const response = await axios.get(GET_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Get ranking of squads in a tournament
 */
export const getRankingOfTournament = async (data: RankingTournament, token: string) => {
  const response = await axios.get(RANKING_TOURNAMENT, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      per_page: data.per_page
    }
  });
  return response.data;
};

/**
 * Get level progress of squads
 */
export const getLevelProgress = async (token: string) => {
  const response = await axios.get(GET_LEVEL_PROGRESS_OF_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Search for a squad by name
 */
export const searchSquad = async (data: squadSearch, token: string) => {
  const response = await axios.get(SEARCH_SQUAD, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      name: data.name,
    },
  });
  return response.data;
};

/**
 * Create a new squad
 */
export const createSquad = async (data: Squad, token: string) => {
  const response = await axios.post(SQUAD_CREATE, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Add member to a squad using phone number
 */
export const addMembersInSquad = async (data: MemberAddTypes, token: string) => {
  const response = await axios.post(ADD_MEMBER_IN_SQUAD, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Join a squad using squad ID
 */
export const joinMember = async (squad_id: string, token: string) => {
  const response = await axios.post(JOIN_MEMBER, null, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: { squad_id }
  });
  return response.data;
};
