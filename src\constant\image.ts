// File: src/components/images.js
// -------------------- Imports --------------------

// Game Logos
import Plinko from '../assets/game_logo/plinko.avif';

import QuickHustule from '../assets/game_logo/highlow.avif';
import bitCoins from '../assets/game_logo/dice.avif';
import CryptoKing from '../assets/game_logo/crypto.avif';

// Home Banner Images
import Banner_1 from "../assets/banner/dealer_level_up.avif";
import Banner_2 from "../assets/banner/dragonmelon_photorealistic_Nigerian_GTA-style_character_watch_75f3fdb2-1494-4ec0-acfb-eacc133ded4e_3.avif";
import Banner_3 from "../assets/banner/Squads.avif";
import Banner_4 from "../assets/banner/lottery.avif";

import logo from '../assets/images/logo-white.avif';

// Card Images
import Card from '../assets/images/card_1.avif';
import Card_2 from '../assets/images/card_2.avif';
import Card_3 from '../assets/images/card_3.avif';
import Card_4 from '../assets/images/card_4.avif';
import Card_5 from '../assets/images/card_5.avif';
import Card_6 from '../assets/images/card_6.avif';
import Card_7 from '../assets/images/card_7.avif';
import Card_8 from '../assets/images/card_8.avif';
import Card_9 from '../assets/images/card_9.avif';
import Card_10 from '../assets/images/card_10.avif';
import Card_11 from '../assets/images/card_11.avif';
import Card_12 from '../assets/images/card_12.avif';
import Card_13 from '../assets/images/card_13.avif';

// Loading Backgrounds
import loadingBg1 from '../assets/images/loading-bg-1.webp';
import loadingBg2 from '../assets/images/loading-bg-2.webp';
import loadingBg3 from '../assets/images/loading-bg-3.webp';
import loadingBg4 from '../assets/images/loading-bg-4.webp';
import loadingBg5 from '../assets/images/loading-bg-5.webp';

// Spinners
import spinner1 from '../assets/images/spinner-1.avif';
import spinner2 from '../assets/images/spinner-2.avif';
import spinner3 from '../assets/images/spinner-3.avif';
import spinner4 from '../assets/images/spinner-4.avif';
import spinner5 from '../assets/images/spinner-5.avif';
import WatchIcon from '../assets/game_logo/Dimaond.avif'; // “watch video” icon
import MoreWaysIcon from '../assets/game_logo/Dimaond.avif';
import SpinnigWheel from '../assets/game_logo/Spinning_Wheel.avif';
import ScratchCard from '../assets/game_logo/Scratch_Card.avif';

// Scratch Icons and Background
import ScratchBg from '../assets/images/background.avif';
import cupImg from '../assets/images/Scratch-cap.avif';
import dollarImg from '../assets/images/Scratch-doller.avif';
import tokenImg from '../assets/images/Scratch-token.avif';
import artImg from '../assets/images/Scratch-art.avif'; // default
import lootbox from '../assets/game_logo/lootbox.avif';
import scratchGameWelcome from '../assets/games/scratch/scratch-game-welcome.avif';

// Scratch Cards
import scratchCard1 from '../assets/games/scratch/scratch-card-1.avif';
import scratchCard235 from '../assets/games/scratch/scratch-card-2-3-5.avif';
import scratchCard4 from '../assets/games/scratch/scratch-card-4.avif';
import scratchCard6 from '../assets/games/scratch/scratch-card-6.avif';
import scratchCard7 from '../assets/games/scratch/scratch-card-7.avif';
import scratchCard8 from '../assets/games/scratch/scratch-card-8.avif';
import scratchCard9 from '../assets/games/scratch/scratch-card-9.avif';

// Scratch Frames
import capFrames from '../assets/games/scratch/frames-cap.png';
import arTimeFrames from '../assets/games/scratch/frames-artime.png';
import dollarFrames from '../assets/games/scratch/frames-dollar.png';
import dollar100Frames from '../assets/games/scratch/frames-dollar-100.png';
import tokenFrames from '../assets/games/scratch/frames-token.png';
import token100Frames from '../assets/games/scratch/frames-token-100.png';

// Others
import eclipseBg from '../assets/images/eclipse-bg.png';

// Maps
import map_1 from '../assets/map/map_1.avif';
import map_2 from '../assets/map/map_2.avif';
import map_3 from '../assets/map/map_3.avif';
import map_4 from '../assets/map/map_4.avif';
import map_5 from '../assets/map/map_5.avif';
import map_6 from '../assets/map/map_6.avif';
import map_7 from '../assets/map/map_7.avif';
import map_8 from '../assets/map/map_8.avif';
import map_9 from '../assets/map/map_9.avif';
import map_10 from '../assets/map/map_10.avif';
import map_11 from '../assets/map/map_11.avif';
import map_12 from '../assets/map/map_12.avif';
import lock from '../assets/map/Lock.avif';

// Sellers Images
import capo from '../assets/sellers/CAPO.avif';
import cannector from '../assets/sellers/CONNECTOR.avif';
import corner_hustle from '../assets/sellers/CORNER_HUSTLER.avif';
import kingpin from '../assets/sellers/KINGPIN.avif';
import og from '../assets/sellers/OG.avif';
import shot_caller from '../assets/sellers/SHOT_CALLER.avif';
import shot_caller_2 from '../assets/sellers/SHOT_CALLER_2.avif';
import shot_caller_3 from '../assets/sellers/SHOT_CALLER_3.avif';
import street_boss from '../assets/sellers/STREET_BOSS.avif';
import Street_scout from '../assets/sellers/STREET_SCOUT.avif';
import underboss from '../assets/sellers/UNDERBOSS.avif';

// Spinning wheel game
import light from '../assets/games/spinning_wheel/light.svg';
import wheelSpear from '../assets/games/spinning_wheel/wheel-spear.svg';
import segmentBorder from '../assets/games/spinning_wheel/segment-border.png';
import baseballCap from '../assets/games/spinning_wheel/baseball-cap.png';
import better from '../assets/games/spinning_wheel/diamond.svg';
import dollar from '../assets/games/spinning_wheel/dollar.svg';
import internetTraffic from '../assets/games/spinning_wheel/internet-traffic.svg';
import mystery from '../assets/games/spinning_wheel/mistery.avif';
import wheelBorders from '../assets/games/spinning_wheel/wheel-borders.svg';

// Plinko game
import plinkoFieldBg from '../assets/games/plinko/plinko-field-bg.avif';

// Squad Banner Image
import squad_image from '../assets/banner/Squads.png';
import coins from '../assets/game_logo/coins.png';

// -------------------- Exported Images Object --------------------

export const IMAGES = {
  // Game Logos
  PLINKO: Plinko,
  QUICK_HUSTULE: QuickHustule,
  BIT_COINS: bitCoins,
  CRYPTO_KING: CryptoKing,
  WATCH_ICON: WatchIcon,
  MOREWAYS_ICON: MoreWaysIcon,
  SPINNING_WHEEL: SpinnigWheel,
  SCRATCH_CARD: ScratchCard,
  LOOTBOX: lootbox,

  // Cards
  CARD_1: Card,
  CARD_2: Card_2,
  CARD_3: Card_3,
  CARD_4: Card_4,
  CARD_5: Card_5,
  CARD_6: Card_6,
  CARD_7: Card_7,
  CARD_8: Card_8,
  CARD_9: Card_9,
  CARD_10: Card_10,
  CARD_11: Card_11,
  CARD_12: Card_12,
  CARD_13: Card_13,

  // Loading Backgrounds
  LOADING_BG_1: loadingBg1,
  LOADING_BG_2: loadingBg2,
  LOADING_BG_3: loadingBg3,
  LOADING_BG_4: loadingBg4,
  LOADING_BG_5: loadingBg5,

  // Spinners
  SPINNER_1: spinner1,
  SPINNER_2: spinner2,
  SPINNER_3: spinner3,
  SPINNER_4: spinner4,
  SPINNER_5: spinner5,

  // Scratch Background and Icons
  SCRATCH_BG: ScratchBg,
  SCRATCH_CUP: cupImg,
  SCRATCH_DOLLAR: dollarImg,
  SCRATCH_TOKEN: tokenImg,
  SCRATCH_ART: artImg,

  // Scratch Game
  SCRATCH_CARD_1: scratchCard1,
  SCRATCH_CARD_2: scratchCard235,
  SCRATCH_CARD_3: scratchCard235,
  SCRATCH_CARD_4: scratchCard4,
  SCRATCH_CARD_5: scratchCard235,
  SCRATCH_CARD_6: scratchCard6,
  SCRATCH_CARD_7: scratchCard7,
  SCRATCH_CARD_8: scratchCard8,
  SCRATCH_CARD_9: scratchCard9,
  CAP_FRAMES: capFrames,
  ARTIME_FRAMES: arTimeFrames,
  DOLLAR_FRAMES: dollarFrames,
  DOLLAR_100_FRAMES: dollar100Frames,
  TOKEN_FRAMES: tokenFrames,
  TOKEN_100_FRAMES: token100Frames,
  SCRATCH_GAME_WELCOME: scratchGameWelcome,

  // Others
  ECLIPSE_BG: eclipseBg,

  // Maps
  MAP_1: map_1,
  MAP_2: map_2,
  MAP_3: map_3,
  MAP_4: map_4,
  MAP_5: map_5,
  MAP_6: map_6,
  MAP_7: map_7,
  MAP_8: map_8,
  MAP_9: map_9,
  MAP_10: map_10,
  MAP_11: map_11,
  MAP_12: map_12,
  LOCK: lock,
  SHOT_CALLER_3: shot_caller_3,
  STREET_SCOUT: Street_scout,

  // seller images
  CAPO: capo,
  CONNECTOR: cannector,
  CORNER_HUSTLE: corner_hustle,
  KINGPIN: kingpin,
  OG: og,
  SHOT_CALLER: shot_caller,
  SHOTE_CELLER_2: shot_caller_2,
  STREET_BOSS: street_boss,
  UNDERBOSS: underboss,

  // Spinning wheel game
  LIGHT: light,
  WHEEL_SPEAR: wheelSpear,
  SEGMENT_BORDER: segmentBorder,
  BASEBALL_CAP: baseballCap,
  BETTER: better,
  DOLLAR: dollar,
  INTERNET_TRAFFIC: internetTraffic,
  MYSTERY: mystery,
  WHEEL_BORDERS: wheelBorders,

  // Plinko
  PLINKO_FIELD_BG: plinkoFieldBg,

  // squad images
  SQUAD_BANNER_IMAGE: squad_image,
  COINS: coins,

  // logo
  LOGO: logo,

  // home page banner
  BANNER_1: Banner_1,
  BANNER_2: Banner_2,
  BANNER_3: Banner_3,
  BANNER_4: Banner_4
};
