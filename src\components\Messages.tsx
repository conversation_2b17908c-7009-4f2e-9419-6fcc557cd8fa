import React, { useEffect, useState } from 'react';
import { X, <PERSON>, Gift, Co<PERSON>, Star, MessageCircle, Trash2, Eye, Loader2 } from 'lucide-react';
import {
  getNotification,
  markAllReadNotification,
  markReadNotificationByID,
  deletNotificationById,
} from '../api/notificationService';
import { useAuth } from '../auth/AuthContext';

interface MessagesProps {
  onClose: () => void;
}

const iconMap = {
  Reward: Gift,
  Achievement: Star,
  Bonus: Coins,
  System: MessageCircle,
};

const PAGE_LIMIT = 10;

const Messages: React.FC<MessagesProps> = ({ onClose }) => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const { accessToken } = useAuth();

  useEffect(() => {
    fetchNotifications(1, true);
  }, []);

  const fetchNotifications = async (pageNumber: number, reset = false) => {
    if (!accessToken || loading) return;
    try {
      setLoading(true);
      const res = await getNotification({ page: pageNumber, per_page: PAGE_LIMIT }, accessToken);
      if (reset) {
        setNotifications(res.notifications);
      } else {
        setNotifications((prev) => [...prev, ...res.notifications]);
      }
      setHasMore(res.notifications.length === PAGE_LIMIT);
      setPage(pageNumber);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAllRead = async () => {
    if (!accessToken) return;
    try {
      await markAllReadNotification(accessToken);
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
    } catch (error) {
      console.error('Error marking all read:', error);
    }
  };

  const handleMarkAsRead = async (id: string) => {
    if (!accessToken) return;
    try {
      await markReadNotificationByID(id, accessToken);
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n))
      );
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!accessToken) return;
    try {
      await deletNotificationById(id, accessToken);
      setNotifications((prev) => prev.filter((n) => n.id !== id));
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    setTimeout(() => setIsAnimating(true), 50);

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') handleClose();
    };
    window.addEventListener('keydown', handleEscape);

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(onClose, 300);
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchNotifications(page + 1);
    }
  };

  const containerClass = isMobile
    ? `fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center transition-all duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}`
    : `fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}`;

  const contentClass = isMobile
    ? `bg-gradient-to-b from-[#510957] to-black rounded-t-3xl w-full max-h-[85vh] flex flex-col transform transition-transform duration-300 ease-out ${isAnimating ? 'translate-y-0' : 'translate-y-full'}`
    : `bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-md max-h-[85vh] flex flex-col transform transition-all duration-300 ease-out ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`;

  return (
    <div className={containerClass}>
      <div className={contentClass}>
        <div className={`p-6 flex justify-between items-center sticky top-0 bg-[#510957] ${isMobile ? 'rounded-t-3xl' : 'rounded-t-2xl'} z-10`}>
          <div className="flex items-center gap-3">
            <Bell size={20} className="text-yellow-400" />
            <h2 className="text-xl font-bold text-white font-[Anton] tracking-wide">Messages</h2>
          </div>
          <div className="flex gap-3">
            <button onClick={handleMarkAllRead} className="text-sm text-white/60 hover:text-white">Mark All Read</button>
            <button onClick={handleClose} className="text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full">
              <X size={24} />
            </button>
          </div>
        </div>

        {isMobile && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-1 bg-white/30 rounded-full"></div>
          </div>
        )}

        <div className="flex-1 overflow-y-auto divide-y divide-white/10">
          {notifications.length > 0 ? (
            <>
              {notifications.map((n) => {
                const Icon = iconMap[n.type as keyof typeof iconMap] || MessageCircle;
                return (
                  <div
                    key={n.id}
                    className={`p-6 hover:bg-white/10 transition-colors ${!n.read ? 'bg-white/10' : ''}`}
                  >
                    <div className="flex items-start gap-4">
                      <div className="p-3 rounded-xl bg-white/10">
                        <Icon size={24} className="text-yellow-400" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-1">
                          <h3 className="font-medium text-white">
                            {n.title}
                            {!n.read && (
                              <span className="ml-2 inline-block px-2 py-0.5 text-xs bg-yellow-400 text-black rounded-full">New</span>
                            )}
                          </h3>
                          <div className="flex gap-2">
                            {!n.read && <Eye size={16} className="text-white/50 cursor-pointer" onClick={() => handleMarkAsRead(n.id)} />}
                            <Trash2 size={16} className="text-red-400 cursor-pointer" onClick={() => handleDelete(n.id)} />
                          </div>
                        </div>
                        <p className="text-sm text-white/60 mb-2">{n.content}</p>
                        <p className="text-xs text-white/40">{new Date(n.created_at).toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={loading}
                    className="text-white/70 hover:text-white px-4 py-2 border border-white/20 rounded-lg"
                  >
                    {loading ? <Loader2 size={18} className="animate-spin inline-block mr-2" /> : 'Load More'}
                  </button>
                </div>
              )}
            </>
          ) : loading ? (
            <div className="p-8 text-center text-white/60">
              <Loader2 size={40} className="mx-auto mb-4 animate-spin opacity-40" />
              <p>Loading messages...</p>
            </div>
          ) : (
            <div className="p-8 text-center text-white/60">
              <MessageCircle size={40} className="mx-auto mb-4 opacity-40" />
              <p>No messages yet</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Messages;
