// Imports
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Users, Share2, Copy, Plus, ExternalLink, CheckCircle, X, Info, Trash, } from 'lucide-react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import OptimizedImage from '../components/OptimizedImage';
import Whatsaap from '../assets/images/wapp.svg';
import TeliGram from '../assets/images/Artboard.svg';
import email from '../assets/images/email.svg';
import * as Yup from 'yup';
import { IMAGES } from '../constant/image';
import { addMembersInSquad, createSquad, deleteMember, deleteSquad, getLevelProgress, getRankingOfTournament, getSquad, getTotalErans, joinMember, levelSquad, searchSquad, squadMembers } from '../api/squad';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';
import ConfirmModal from '../components/confirmationModal';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useSocketContext } from '../context/socketProvider';

//#region Utility Functions

/**
 * Formats a number or numeric string into a localized string with commas,
 * removing any decimal part.
 * @param {number | string} value - The value to format.
 * @returns {string} Formatted number string without decimals.
 */
const formatCoins = (value: number | string): string =>
  Math.floor(Number(value)).toLocaleString();

//#endregion

//#region Types & Interfaces

interface ScuadType {
  id: string,
  handle: string,
  type: string,
}

interface RankingTable {
  id: string,
  rank: number,
  handle: string,
  total_earns: string
}

type SquadMember = {
  id: string;
  avatar: string,
  squad_id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  created_at: string;
  updated_at: string;
};

type User = {
  id: string;
  first_name: string;
  last_name: string;
  phone: string;
};

type Squad = {
  id: string;
  handle: string;
  type: string;
  user: User;
  squad_memebers: SquadMember[];
};

// If this is an array of squads
type SquadList = Squad[];

type ProgressionRow = {
  level: number;
  rankLabel: string;
  cumulative: number;
  reward: string;
};

interface CreateSquadFormValues {
  name: string;
  joiningType: string;
}

interface HeroBannerProps {
  amountRemaining: number;
  nextLevel: number;
  backgroundUrl: string;    // URL for your banner background
}

//#endregion

//#region Constants

const SHARE_OPTIONS = [
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: Whatsaap,
    color: 'text-green-400',
    bgColor: 'bg-green-400/20'
  },
  {
    id: 'telegram',
    name: 'Telegram',
    icon: TeliGram,
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/20'
  },
  {
    id: 'email',
    name: 'Email',
    icon: email,
    color: 'text-purple-400',
    bgColor: 'bg-purple-400/20'
  }
];

const CREATE_SQUAD_SCHEMA = Yup.object().shape({
  name: Yup.string()
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('Squad Name is required'),
  joiningType: Yup.string()
    .oneOf(['Open', 'Request to Join', 'Invite Only'], 'Invalid joining type')
    .required('Joining Type is required'),
});

//#endregion

//#region Components

/**
 * Popup to show squad level progression table.
 */
const ProgressionPopup: React.FC<{ data: ProgressionRow[]; onClose: () => void }> = ({ data, onClose }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);

    // Animate in
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
    bg-gradient-to-b from-[#510957] to-black rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center
    transition-opacity duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
    bg-gradient-to-br from-[#510957] to-black rounded-2xl w-full max-w-2xl p-6 relative
    transform transition-all duration-300 ease-out ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
  `;

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
          <h3 className="text-white text-xl font-[Anton] tracking-wide">🧗 Squad Level Progression</h3>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>

        <div className="overflow-y-auto px-4 pb-6">
          <table className="min-w-full bg-white/5 rounded-lg overflow-hidden text-white text-sm">
            <thead className="bg-white/10">
              <tr>
                <th className="px-4 py-2 text-left">Level</th>
                <th className="px-4 py-2 text-left">Rank</th>
                <th className="px-4 py-2 text-left">Cumulative</th>
                <th className="px-4 py-2 text-left">Reward</th>
                {/* <th className="px-4 py-2 text-left">Notes</th> */}
              </tr>
            </thead>
            <tbody>
              {data.map(row => (
                <tr key={row.level} className="border-t border-white/10 hover:bg-white/10">
                  <td className="px-4 py-2">{row.level}</td>
                  <td className="px-4 py-2">{row.rankLabel}</td>
                  <td className="px-4 py-2">{row.cumulative}</td>
                  <td className="px-4 py-2">{row.reward}</td>
                  {/* <td className="px-4 py-2">{row.notes}</td> */}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

/**
 * Popup to show when squad levels up.
 */
const LevelUpPopup: React.FC<{ level: number; onClose: () => void }> = ({ level, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="relative bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 max-w-md w-full text-center transform transition-all duration-300 scale-100 opacity-100">
        {/* Close icon at top-right */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-yellow-300 focus:outline-none"
          aria-label="Close Level Up Popup"
        >
          <X size={24} />
        </button>

        <h2 className="text-3xl font-bold text-yellow-300 mb-4">🎉 Level Up! 🎉</h2>
        <p className="text-white text-lg mb-6">Your squad has reached Level {level}!</p>
        <button
          onClick={onClose}
          className="px-6 py-2 bg-yellow-300 text-black font-semibold rounded-lg hover:bg-yellow-400 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

/**
 * Hero banner for the squads page.
 */
const HeroBanner: React.FC<HeroBannerProps> = ({
  backgroundUrl,
}) => (
  <div
    className="relative w-full h-40 rounded-2xl overflow-hidden mb-6"
    style={{ backgroundImage: `url(${backgroundUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
  >
    {/* Dark overlay */}
    <div className="absolute inset-0 bg-black/35"></div>
    {/* Work together. Earn More. */}
    {/* Content */}
    <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between h-full px-6">
      <div>
        <h2 className="text-white font-[Anton] text-xl sm:text-2xl tracking-wide">
          Work together.
        </h2>
        <span className="font-bold font-[Anton] text-yellow-300 text-xl tracking-wide block">
          Earn More.
        </span>
      </div>
    </div>

  </div>
);

//#endregion

/**
 * Main Squads Page Component
 */
const SquadsPage = () => {
  // -------------------- State Hooks --------------------
  // Navigation and Auth
  const { accessToken } = useAuth();

  // Squad and UI State
  const [levelProgressData, setLevelProgressData] = useState([]);
  const [memberID, setMemberID] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpenForSquad, setIsModalOpenForSquad] = useState(false);
  const [squadID, setSquadID] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [squad, setSquad] = useState<SquadList>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showProgression, setShowProgression] = useState(false);
  const [rankingData, setRankingData] = useState<RankingTable[]>([]);
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [leveledTo, setLeveledTo] = useState(1);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isPhoneLoading, setIsPhoneLoading] = useState<boolean>(false);
  const [showAddMemberPopup, setShowAddMemberPopup] = useState(false);
  const [totalEarning, setTotalEarning] = useState({ earns: 0 });
  // For squad search/join
  const [searchTerm, setSearchTerm] = useState('');
  const [searchError, setSearchError] = useState('');
  // Hero values (replace with dynamic logic as needed)
  const nextLevel = 7;
  const thresholdForNext = 30000;
  const currentPoints = 20;
  const amountRemaining = Math.max(thresholdForNext - currentPoints, 0);
  const [squadResult, setSquadResult] = useState<ScuadType | null>(null);
  const [loading, setLoading] = useState(false);
  const [rankListLoading, setRankListLoading] = useState(false);


  // const [phoneNumber, setPhoneNumber] = useState('');
  const [phoneError, setPhoneError] = useState('');

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/[^0-9]/g, '');

    // Only allow 10 digits
    const trimmedValue = rawValue.slice(0, 10);
    setPhoneNumber(trimmedValue);

    // Validation check
    if (trimmedValue.length < 10) {
      setPhoneError('Phone number must be 10 digits.');
    } else if (!/^[789]\d{9}$/.test(trimmedValue)) {
      setPhoneError('Phone number must start with 7, 8, or 9.');
    } else {
      setPhoneError('');
    }
  };

  // Socket and user profile
  const {
    squadLevelState,
    connectSquadLevelSocket
  } = useSocketContext();
  const { data: userProfile, error } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  // Check if current user is squad owner
  const isMatch = userProfile?.user_id === squad[0]?.user?.id;

  // -------------------- Effects --------------------

  // Connect socket on mount
  useEffect(() => {
    connectSquadLevelSocket();
  }, []);

  // Fetch squad and related data on mount
  useEffect(() => {
    let isMounted = true;
    const fetchData = async () => {
      try {
        if (isMounted) {
          await fetchLevelProgress();
          await fetchTotalEarning();
          await fetchSquadWithMember();
          await fetchTournamentRanking();
        }
      } catch (error) {
        console.error("Error fetching squad with members:", error);
      }
    };
    fetchData();
    return () => { isMounted = false; };
  }, []);

  // Fetch total earning when squad changes
  useEffect(() => {
    fetchTotalEarning();
  }, [squad]);

  // Debounced squad search
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      if (searchTerm.trim().length > 0) {
        fetchSquadByName(searchTerm);
      }
    }, 500);
    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Show level up popup if needed
  useEffect(() => {
    if (amountRemaining === 0 && !showLevelUp) {
      setLeveledTo(nextLevel);
      setShowLevelUp(true);
    }
  }, [amountRemaining, nextLevel, showLevelUp]);

  // Responsive check for mobile/desktop
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    setTimeout(() => setIsAnimating(true), 50);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // -------------------- API Functions --------------------

  /**
   * Search squad by name.
   */
  const fetchSquadByName = async (name: string) => {
    try {
      // setLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const response = await searchSquad({ name }, accessToken)
      setSquadResult(response);
    } catch (error) {
      console.log(error)
    } finally {
      // setLoading(false)
    }
  };

  /**
   * Fetch squad with members.
   */
  const fetchSquadWithMember = async () => {
    try {
      setLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const response = await getSquad(accessToken)
      // console.log()

      // await fetchSquadByID(response[0]?.id)
      setSquad(response)
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  };

  /**
   * Fetch squad level progression.
   */
  const fetchLevelProgress = async () => {
    try {
      setLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const response = await getLevelProgress(accessToken)

      const formatted = response.map((item: any) => ({
        level: item.level,
        rankLabel: item.rank,
        cumulative: item.cumulative_points,
        reward: item.rewards.map((r: any) => `${r.amount} ${r.type}`).join(', ')
      }));
      setLevelProgressData(formatted)
      // console.table( response)

    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  };

  /**
   * Fetch total earning of squad.
   */
  const fetchTotalEarning = async () => {
    try {
      setLoading(true)
      if (!accessToken) throw new Error('Access token missing');
      const response = await getTotalErans(squad[0].id, accessToken)
      setTotalEarning(response)
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  };

  /**
   * Fetch tournament ranking.
   */
  const fetchTournamentRanking = async () => {
    try {
      setRankListLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const payload = {
        page: 1,
        per_page: 10
      }
      const response = await getRankingOfTournament(payload, accessToken)
      setRankingData(response?.ranking)
    } catch (error) {
      console.log("🚀 ~ fetchTournamentRanking ~ error:", error)
    } finally {
      setRankListLoading(false)
    }
  };

  // -------------------- Handlers --------------------

  /**
   * Copy invite link to clipboard.
   */
  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  /**
   * Handle delete member (open modal).
   */
  const handleDelete = (id: string) => {

    // console.table("delete time data",id)
    // return
    setMemberID(id)
    setIsModalOpen(true)
  };

  /**
   * Handle delete squad (open modal).
   */
  const handleDeleteSquad = (id: string) => {
    setSquadID(id)
    setIsModalOpenForSquad(true)
  };


  /**
   * Confirm delete member.
   */
  const handleConfirmDelete = async () => {
    try {
      if (!accessToken) throw new Error('Access token missing');
      if (!memberID) return; // Ensure memberID is not null

      const response = await deleteMember(memberID, accessToken);
      await fetchSquadWithMember()
      CustomToast('success', 'Member deleted successfully.')
      setIsModalOpen(false)
      console.log(response)
    } catch (error) {
      console.log(error)
    }
  };

  /**
   * Confirm delete squad.
   */
  const handleConfirmDeleteForSquad = async () => {
    try {
      if (!accessToken) throw new Error('Access token missing');
      if (!squadID) return; // Ensure memberID is not null

      const response = await deleteSquad(squadID, accessToken);
      await fetchSquadWithMember()
      CustomToast('success', 'Squad deleted successfully.')
      setIsModalOpenForSquad(false)
      console.log(response)
    } catch (error) {

      console.log(error)
    }
  };

  /**
   * Handle sharing squad invite.
   */
  const handleShare = (platform: string) => {
    let shareUrl = '';
    const inviteUrl = window.location.href;
    const message = `Join my squad on Playzuzu! ${inviteUrl}`;

    switch (platform) {
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
        break;
      case 'telegram':
        shareUrl = `https://t.me/share/url?url=${encodeURIComponent(inviteUrl)}&text=${encodeURIComponent('Join my squad on Playzuzu!')}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=Join my Playzuzu squad&body=${encodeURIComponent(message)}`;
        break;
    }

    window.open(shareUrl, '_blank');
  };

  /**
   * Handle create squad.
   */
  const handleCreateSquad = async (
    values: CreateSquadFormValues,
    { resetForm }: { resetForm: () => void }
  ) => {
    try {
      if (!accessToken) throw new Error('Access token missing');

      const payload = {
        created_at: new Date().toISOString(), // Use ISO format for date
        handle: values.name,
        type: values.joiningType
      };

      const response = await createSquad(payload, accessToken);

      // Optional: Handle success response (e.g., show toast or update UI)
      if (response?.message) {

        CustomToast('success', "Squad Create Successfully.");
      }
      await fetchSquadWithMember()
      resetForm();
      setShowCreateModal(false);
    } catch (err) {
      CustomToast('error', (err as any)?.response?.data?.message)
      console.error('Failed to create squad:', err);
      // Optional: Show error message to user (toast, modal, etc.)
    }
  };

  /**
   * Handle leave squad.
   */
  const handleLeaveSquad = async () => {
    try {
      if (!accessToken) throw new Error('Access token missing');

      if (!squad) return;

      const payload = {
        squad_id: squad[0].id,
        // user_id: squad[0].owner // Replace with correct property if available in Squad type
      }
      const response = await levelSquad(payload, accessToken)
      await fetchSquadWithMember()
      if (response) {
        CustomToast('success', "Squad Leave Successfully.");
      }
    } catch (error) {
      console.error('Error leaving squad:', error);
    }
  };

  /**
   * Add member to squad by phone.
   */
  const handleAddMember = async (squad_id: string) => {
    try {
      setIsPhoneLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const payload = {
        squad_id: squad_id,
        phone: `+234${phoneNumber}`
      }

      // console.table(payload)
      const response = await addMembersInSquad(payload, accessToken)

      if (response) {
        await fetchSquadWithMember()
        CustomToast('success', 'Member added successfully')
        setPhoneNumber("")
      }
    } catch (error) {
      console.error(error)
      CustomToast('error', (error as any)?.response?.data?.message)
    } finally {
      setIsPhoneLoading(false)
    }
  };
  /**
   * Join squad as member.
   */
  const handleJoinMember = async (squad_id: string) => {
    try {
      if (!accessToken) throw new Error('Access token missing');


      const response = await joinMember(squad_id, accessToken)

      if (response) {
        await fetchSquadWithMember()
        CustomToast('success', 'Member added successfully')
        setPhoneNumber("")
      }
    } catch (error) {
      console.error(error)
      CustomToast('error', (error as any)?.response?.data?.message)
    }
  };

  // -------------------- Modal Styles --------------------
  // CSS classes for mobile and desktop modals
  const mobileContainerClass = `
     fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
     transition-all duration-300 ease-out
     ${isAnimating ? 'opacity-100' : 'opacity-0'}
   `;
  const mobileContentClass = `
   bg-gradient-to-b from-[#510957] to-black
     rounded-t-3xl w-full max-h-[85vh] flex flex-col
     transform transition-transform duration-300 ease-out
     ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
   `;
  const desktopContainerClass = `
     fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
     transition-all duration-300 ease-out
     ${isAnimating ? 'opacity-100' : 'opacity-0'}
   `;
  const desktopContentClass = `
    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
   rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
   transform transition-all duration-300 ease-out
   ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
 `;

  // -------------------- Render --------------------
  return (
    <div className="max-w-4xl mx-auto">

      {/* Display Squad Level if in a squad */}
      <HeroBanner backgroundUrl={IMAGES.SQUAD_BANNER_IMAGE} amountRemaining={amountRemaining} nextLevel={nextLevel} />
      {squad.length > 0 && (
        <>
          <div className="flex items-center gap-2 mb-2 px-2">
            <span className="text-white/80 text-sm font-[Anton]">Squad Level:</span>
            {
              !isMatch && (
                <button
                  onClick={handleLeaveSquad}
                  className="ml-4 px-3 py-1 rounded-lg bg-[#ED0CFF] text-white text-xs font-[Anton] hover:bg-[#d30ae0] transition-colors"
                  title="Leave Squad"
                >
                  Leave Squad
                </button>
              )
            }

          </div>
        </>
      )}

      <div className="mb-4 px-2 bg-white/10 rounded-lg p-4 border border-[#ED0CFF]">
        <p className="text-[14px] text-white mb-1 tracking-wide font-[Anton]">Level Progression</p>
        <div className="w-full bg-white/10 rounded-full h-2">
          <div
            className="h-2 rounded-full bg-green-400"
            style={{
              width: `${Math.min(
                (squadLevelState?.amount_spent_to_reach_level ?? 0) /
                (squadLevelState?.next_level_requirement ?? 1) * 100,
                100
              ).toFixed(2)
                }%`,
            }}
          />
        </div>
        <p className="text-[12px] text-white mt-1 font-[Anton] tracking-wide">
          {formatCoins(squadLevelState?.amount_spent_to_reach_level ?? 0)} / {formatCoins(squadLevelState?.next_level_requirement ?? 0)} Bucks to next level
        </p>
      </div>



      <h1 className="text-[20px] mt-2 tracking-wide font-bold w-auto font-[Anton]">Squads</h1>

      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mt-4 mb-6 w-full">
        {/* Show search/join only if NOT in a squad */}
        {squad.length === 0 && (
          <>
            {/* Search + Result */}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (!searchTerm.trim()) return;
                const found = squad.length > 0 && squad.find(
                  (s) =>
                    s.handle.toLowerCase() === searchTerm.trim().toLowerCase()
                );
                if (found) {
                  setSquad([found]);
                  setSearchError('');
                } else {
                  setSearchError('No squad found with that name.');
                }
              }}
              className="flex-1 max-w-lg mx-auto w-full"
            >
              <div className="flex items-center gap-2 relative w-full">
                <input
                  type="text"
                  placeholder="Search or join squad by name"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setSearchError('');
                  }}
                  className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition pr-10"
                  aria-label="Search squad by name"
                />

                {searchTerm && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchTerm('');
                      setSearchError('');
                      setSquadResult(null);
                    }}
                    className="absolute right-3 text-white/50 hover:text-white transition"
                    aria-label="Clear search"
                  >
                    <X size={18} />
                  </button>
                )}
              </div>


              {/* Error or Spacer */}
              <div className="mt-2 min-h-[20px]">
                {searchError ? (
                  <p className="text-red-400 text-sm">{searchError}</p>
                ) : (
                  <p className="invisible text-sm">Placeholder</p>
                )}
              </div>

              {/* Result Card */}
              {squadResult && (
                <div className="mt-4 p-4 bg-white/10 border border-white/20 rounded-lg flex items-center justify-between text-white">
                  <div>
                    <p className="text-sm opacity-80">Squad Found:</p>
                    <p className="font-bold text-lg">
                      {squadResult.handle}
                    </p>
                    <p className="text-xs opacity-70">
                      {squadResult.type} Squad
                    </p>
                  </div>
                  <button
                    onClick={() => { handleJoinMember(squadResult.id) }}
                    className="px-3 py-2 bg-yellow-400 text-black rounded-lg font-medium hover:bg-yellow-500 transition"
                    aria-label={`Join ${squadResult.handle}`}
                  >
                    Join
                  </button>
                </div>
              )}
              {/* </div> */}
            </form>

            {/* Create Squad */}
            <div className="flex justify-center sm:justify-end">
              <button
                onClick={() => setShowCreateModal(true)}
                className="px-4 py-3 rounded-lg flex items-center gap-2 text-[#ED0CFF] border border-[#ED0CFF] hover:bg-[#ED0CFF]/10 transition"
              >
                <Plus size={20} />
                <span className="uppercase font-[Anton] text-[14px] tracking-wide">
                  Create Squad
                </span>
              </button>
            </div>
          </>
        )}
      </div>




      {/* {!squad && !loading && (
       
      )} */}

      {
        loading ? <div className="bg-[#131313] rounded-xl p-4 mb-6 animate-pulse">
          <div className="h-5 bg-white/10 rounded w-1/3 mb-4"></div>
          <div className="flex items-center gap-2 mb-4">
            {[...Array(4)].map((_, idx) => (
              <div key={idx} className="w-10 h-10 rounded-full bg-white/10" />
            ))}
            <div className="ml-2 space-y-2">
              <div className="w-24 h-4 bg-white/10 rounded"></div>
              <div className="w-32 h-3 bg-white/10 rounded"></div>
            </div>
          </div>
          <div className="border-t border-white/10 pt-4 space-y-4">
            {[...Array(3)].map((_, idx) => (
              <div key={idx} className="bg-white/5 h-16 rounded-lg"></div>
            ))}
          </div>
        </div>
          : squad.length > 0 ? (
            <div className="bg-[#131313] rounded-xl p-4 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-[14px] font-[Anton] truncate">{squad[0]?.handle}</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowShareModal(true)}
                    className="transition-colors p-2 rounded-lg hover:bg-white/10"
                    aria-label="Share Squad"
                  >
                    <Share2 size={20} className="text-[#ED0CFF]" />
                  </button>
                  <button
                    onClick={() => setShowProgression(true)}
                    className="transition-colors p-2 rounded-lg hover:bg-white/10"
                    aria-label="Compare Squads"
                  >
                    <Info size={20} className="text-white/80 hover:text-white" />
                  </button>
                  {
                    isMatch &&
                    <button
                      onClick={() => handleDeleteSquad(squad[0]?.id)}
                      className="transition-colors p-2 rounded-lg hover:bg-white/10"
                      aria-label="Compare Squads"
                    >
                      <Trash size={20} className="text-red-500 hover:text-red" />
                    </button>

                  }

                </div>
              </div>


              <div className="flex items-center gap-2 mb-4">
                {squad[0]?.squad_memebers?.map((member, idx) => (
                  <div
                    key={member?.id}
                    className="w-10 h-10 rounded-full bg-gray-600 text-white flex items-center justify-center text-sm font-bold"
                    style={{ marginLeft: idx > 0 ? '-20px' : 0 }}
                  >
                    {member?.avatar ? (
                      <OptimizedImage
                        src={member.avatar}
                        alt={member.first_name}
                        className="w-full h-full rounded-full object-cover"
                        sizes="40px"
                      />
                    ) : (
                      member?.first_name?.charAt(0).toUpperCase()
                    )}
                  </div>
                ))}
                <div className="ml-2">
                  <div>
                    <p className="text-white text-sm font-[Anton]">
                      Owner: {[squad[0]?.user?.first_name, squad[0]?.user?.last_name].filter(Boolean).join(" ")}
                    </p>
                  </div>
                  <p className="text-[14px] font-[Anton]">{squad[0]?.squad_memebers?.length || 0} members</p>
                </div>
              </div>


              <div className="border-t border-white/10 pt-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-white text-[14px] font-[Anton]">
                    {totalEarning?.earns} Bucks
                  </div>
                </div>

                {/* Inline Details */}
                <div>

                  {/* <h3 className="text-lg font-semibold mb-4 font-[Anton] tracking-wide">Squad Progress</h3> */}


                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold font-[Anton] tracking-wide">Squad Members</h3>
                      <button
                        onClick={() => setShowAddMemberPopup(true)}
                        className="flex items-center gap-2 bg-[#ED0CFF] px-3 py-1 rounded-lg hover:bg-[#d30ae0  ] transition-colors text-white text-sm font-[Anton]"
                        title="Add member by ID"
                      >
                        <Plus size={18} />
                        Add Member
                      </button>
                    </div>
                    {/* Add Member by User ID Popup */}
                    {showAddMemberPopup && (
                      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
                        <div className="bg-gradient-to-br from-[#510957] to-black rounded-2xl p-6 w-full max-w-xs shadow-lg relative">
                          <button
                            onClick={() => setShowAddMemberPopup(false)}
                            className="absolute top-3 right-3 text-white/60 hover:text-white"
                            aria-label="Close Add Member Popup"
                          >
                            <X size={22} />
                          </button>
                          <h4 className="text-white text-lg font-anton mb-4">
                            Add Member by Phone Number
                          </h4>

                          <div className="relative mb-6">
                            <label htmlFor="phone-input" className="block text-sm font-medium mb-1 text-white/80">
                              Phone Number
                            </label>
                            <div className={`flex items-center bg-white/10 border rounded-lg transition ${phoneError ? 'border-red-500 ring-2 ring-red-500' : 'border-white/20 focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-400'
                              }`}>
                              <span className="pl-3 pr-2 text-white/70">+234</span>
                              <input
                                id="phone-input"
                                type="tel"
                                value={phoneNumber}
                                onChange={handlePhoneChange}
                                placeholder="e.g., 8012345678"
                                className="w-full bg-transparent px-2 py-2 text-white placeholder-white/50 focus:outline-none"
                                title="Phone Number"
                                aria-describedby="phone-help"
                              />
                            </div>
                            {phoneError && (
                              <p className="text-red-400 text-sm mt-1" id="phone-help">{phoneError}</p>
                            )}
                          </div>

                          {error && <p className="text-sm text-red-400 mb-2">{error}</p>}

                          <button
                            onClick={() => { handleAddMember(squad[0].id); setPhoneNumber(""); setShowAddMemberPopup(false); }}
                            className="w-full flex items-center justify-center gap-2 bg-[#ED0CFF] px-4 py-2 rounded-lg hover:bg-[#d30ae0] transition-colors disabled:opacity-50"
                            disabled={!phoneNumber.trim() || isPhoneLoading}
                          >
                            <Plus size={20} className="text-white" />
                            <span className="text-white font-anton">
                              {isPhoneLoading ? 'Adding...' : 'Add Member'}
                            </span>
                          </button>
                        </div>
                      </div>
                    )}
                    <div className="space-y-4">
                      {squad[0]?.squad_memebers && squad[0].squad_memebers.length > 0 ? (
                        squad[0].squad_memebers.map(member => (
                          <div
                            key={member.id}
                            className="bg-white/5 rounded-lg p-4 flex items-center justify-between border border-[#ED0CFF]"
                          >
                            <div className="flex items-center gap-3">
                              {member.avatar ? (
                                <OptimizedImage
                                  src={member.avatar}
                                  alt={member.first_name}
                                  className="w-10 h-10 rounded-full object-cover"
                                  sizes="40px"
                                />
                              ) : (
                                <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white text-lg font-bold uppercase">
                                  {member.first_name?.charAt(0) || '?'}
                                </div>
                              )}

                              <div>
                                <p className="text-[14px] font-medium font-[Anton]">
                                  {[member.first_name, member.last_name].filter(Boolean).join(' ')}
                                </p>
                                <p className="text-[12px] text-white/60">
                                  Joined{' '}
                                  {member.created_at
                                    ? new Date(member.created_at).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'short',
                                      day: 'numeric',
                                    })
                                    : 'N/A'}
                                </p>
                              </div>
                            </div>

                            {
                              userProfile?.user_id !== member?.id && (<div className="flex flex-col items-end space-y-1">
                                <button
                                  onClick={() => handleDelete(member?.id)}
                                  className="p-1 rounded hover:bg-white/10 focus:outline-none"
                                  aria-label="Remove member"
                                >
                                  <Trash size={20} className="text-red-500" />
                                </button>
                              </div>)
                            }

                          </div>
                        ))
                      ) : (
                        <div className="bg-white/10 py-4 px-3 rounded-md text-center">
                          <p className="text-white text-sm opacity-70">
                            🚫 No members added yet.
                          </p>
                        </div>

                      )}
                    </div>


                  </div>

                </div>
              </div>
            </div>
          ) : (<div className="text-center py-12">
            <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users size={32} className="text-white/60" />
            </div>
            <h2 className="text-xl font-bold mb-2">No Squads Yet</h2>
            <p className="text-white/60 mb-6">Create a squad to play together and earn rewards!</p>
          </div>)
      }

      {
        rankingData.length > 0 && (
          <div className="mb-6 px-2 bg-white/10 rounded-lg p-4">
            <h2 className="text-white font-[Anton] text-lg mb-4 tracking-wide">
              Leaderboard (Last 6 days)
            </h2>
            <table className="min-w-full bg-white/5 rounded-lg overflow-hidden text-white text-sm">
              <thead className="bg-white/10">
                <tr>
                  <th className="px-4 py-2 text-left">Rank</th>
                  <th className="px-4 py-2 text-left">Squad</th>
                  <th className="px-4 py-2 text-left">Bucks Won</th>
                </tr>
              </thead>
              <tbody>
                {rankListLoading ? (
                  // Show 5 skeleton rows
                  [...Array(5)].map((_, idx) => (
                    <tr key={idx} className="border-t border-white/10 animate-pulse">
                      <td className="px-4 py-2">
                        <div className="h-4 w-6 bg-white/20 rounded" />
                      </td>
                      <td className="px-4 py-2">
                        <div className="h-4 w-24 bg-white/20 rounded" />
                      </td>
                      <td className="px-4 py-2">
                        <div className="h-4 w-16 bg-white/20 rounded" />
                      </td>
                    </tr>
                  ))
                ) : rankingData?.length > 0 ? (
                  rankingData.map((row, index) => (
                    <tr
                      key={row.id || `${row.rank}-${row.handle}` || index}
                      className="border-t border-white/10 hover:bg-white/10"
                    >
                      <td className="px-4 py-2">{row.rank}</td>
                      <td className="px-4 py-2">{row.handle}</td>
                      <td className="px-4 py-2">{formatCoins(row.total_earns || 0)}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="px-4 py-4 text-center text-white/60">
                      No data found.
                    </td>
                  </tr>
                )}

              </tbody>
            </table>
          </div>
        )
      }
      {
        showLevelUp && (
          <LevelUpPopup level={leveledTo} onClose={() => setShowLevelUp(false)} />
        )
      }

      {/* Comparison Popup */}
      {showProgression && <ProgressionPopup data={levelProgressData} onClose={() => setShowProgression(false)} />}
      {
        showShareModal && (
          <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
            <div className={isMobile ? mobileContentClass : desktopContentClass}>
              <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
                <h2 className="text-xl text-white font-[Anton] tracking-wide">Share Squad Invite</h2>
                <button
                  onClick={() => setShowShareModal(false)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="p-6">
                <div className="bg-white/5 rounded-lg p-4 mb-6 flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 mr-4">
                    <ExternalLink size={20} className="text-white/60" />
                    <input
                      type="text"
                      value={window.location.href}
                      readOnly
                      className="bg-transparent flex-1 focus:outline-none"
                    />
                  </div>
                  <button
                    onClick={handleCopyLink}
                    className="bg-white/10 hover:bg-white/20 transition-colors p-2 rounded-lg relative group"
                  >
                    {copied ? (
                      <CheckCircle size={20} className="text-green-400" />
                    ) : (
                      <Copy size={20} className="text-white/60 group-hover:text-white" />
                    )}
                  </button>
                </div>

                <div className="space-y-4">
                  {SHARE_OPTIONS.map(option => {
                    // const IconComponent = option.icon;
                    return (
                      <button
                        key={option.id}
                        onClick={() => handleShare(option.id)}
                        className="w-full bg-white/5 hover:bg-white/10 transition-colors rounded-lg p-4 flex items-center gap-4"
                      >
                        <div className={`p-2 rounded-lg ${option.bgColor}`}>
                          <OptimizedImage src={option?.icon} alt="icon" className="h-6 w-6 object-contain" sizes="24px" />
                        </div>
                        <span className="font-medium font-[Anton] tracking-wide">Share via {option.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )
      }

      {/* Create Squad Modal */}
      {
        showCreateModal && (
          <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
            <div className={isMobile ? mobileContentClass : desktopContentClass}>
              <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
                <h2 className="text-xl text-white font-[Anton] tracking-wide">Create New Squad</h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="p-6">
                <Formik
                  initialValues={{ name: '', joiningType: '' }}
                  validationSchema={CREATE_SQUAD_SCHEMA}
                  onSubmit={handleCreateSquad}
                >
                  {({ isSubmitting }) => (
                    <Form>
                      <div className="mb-6">
                        <label htmlFor="name" className="block text-sm font-medium mb-2">
                          Squad Name
                        </label>
                        <Field
                          id="name"
                          name="name"
                          placeholder="Enter squad name"
                          className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                        />
                        <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="joiningType" className="block text-sm font-medium mb-2 text-white">
                          Joining Type
                        </label>
                        <Field
                          as="select"
                          id="joiningType"
                          name="joiningType"
                          className="w-full px-4 py-2 rounded-lg bg-white/10 text-white border border-gray-600 focus:outline-none focus:border-yellow-400"
                        >
                          <option value="" className="bg-gray-800 text-white">Select type</option>
                          <option value="Open" className="bg-gray-800 text-white">Open</option>
                          <option value="Request to Join" className="bg-gray-800 text-white">Request to Join</option>
                          <option value="Invite Only" className="bg-gray-800 text-white">Invite Only</option>
                        </Field>
                        <ErrorMessage name="joiningType" component="div" className="text-red-500 text-sm mt-1" />
                      </div>



                      <div className="flex gap-4">
                        <button
                          type="button"
                          onClick={() => setShowCreateModal(false)}
                          className="flex-1 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors uppercase font-[Anton] tracking-wide"
                          disabled={isSubmitting}
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="flex-1 py-2 rounded-lg bg-white text-black transition-colors uppercase font-[Anton] tracking-wide"
                          disabled={isSubmitting}
                        >
                          Create Squad
                        </button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        )
      }

      {isModalOpen && <ConfirmModal visible={isModalOpen} onCancel={() => setIsModalOpen(false)} onConfirm={handleConfirmDelete} />}
      {isModalOpenForSquad && <ConfirmModal description='Are you sure you want to remove this Squad?' visible={isModalOpenForSquad} onCancel={() => setIsModalOpenForSquad(false)} onConfirm={handleConfirmDeleteForSquad} />}
    </div >
  );
};

export default SquadsPage;