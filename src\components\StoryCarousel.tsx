import React from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { IMAGES } from '../constant/image';
const STORY_SLIDES = [

  {
    title: "From nothing. To king",
    image: IMAGES.BANNER_1,
  },
  {
    title: "Play free. Earn real.",
    image: IMAGES.BANNER_2,
  },
  {
    title: "Work together. Earn more.",
    image: IMAGES.BANNER_3,
  },
  {
    title: "Enter daily. Win big.",
    image: IMAGES.BANNER_4
  },
];

const StoryCarousel = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });

  const scrollPrev = React.useCallback(() => emblaApi && emblaApi.scrollPrev(), [emblaApi]);
  const scrollNext = React.useCallback(() => emblaApi && emblaApi.scrollNext(), [emblaApi]);

  return (
    <div className="relative mb-8 group">
      <div className="overflow-hidden rounded-2xl" ref={emblaRef}>
        <div className="flex">
          {STORY_SLIDES.map((slide, idx) => (
            <div key={idx} className="relative flex-[0_0_100%] min-w-0">
              <div className="relative w-full h-40 rounded-2xl overflow-hidden mb-6">
                <img
                  src={slide.image}
                  alt={slide.title}
                  className=" inset-0 w-full h-full object-cover"
                />
                {/* dark gradient overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-35" />

                {/* text overlay aligned left and centered vertically */}
                <div className="absolute inset-0 flex items-center justify-start p-8 md:p-[26px] mt-[3px] text-left">
                  <p
                    className="text-white font-[Anton] text-xl sm:text-2xl tracking-wide"
                  >
                    {slide.title.split('.').filter(line => line.trim() !== '').map((line, idx) => (
                      <span
                        key={idx}
                        className={`block ${idx === 0 ? 'text-white' : 'text-yellow-300' } ${idx === 1 &&'text-xl'}`}
                      >
                        {line.trim()}
                        {line.trim().endsWith('.') ? '' : '.'}
                      </span>
                    ))}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={scrollPrev}
        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <ChevronLeft size={24} />
      </button>
      <button
        onClick={scrollNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <ChevronRight size={24} />
      </button>
    </div>
  );
};

export default StoryCarousel;