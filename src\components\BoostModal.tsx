import React, { useState, useEffect } from 'react';
import { X, Zap, Facebook, Twitter, Instagram } from 'lucide-react';
import { postRewardClaim } from '../api/rewardsService';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';

interface BoostModalProps {
  onClose: () => void;
  onWatchAd: () => void;
  onShare: (platform: string) => void;
}

const BOOST_OPTIONS = [
  {
    id: 'watch_ad',
    title: 'Watch Video',
    description: 'Watch a short video to earn Bucks',
    icon: Zap,
    xp: 50,
  },
  {
    id: 'share_facebook',
    title: 'Share on Facebook',
    description: 'Share your progress with friends',
    icon: Facebook,
    xp: 100,
  },
  {
    id: 'share_twitter',
    title: 'Share on Twitter',
    description: 'Tweet about your achievements',
    icon: Twitter,
    xp: 100,
  },
  {
    id: 'share_instagram',
    title: 'Share on Instagram',
    description: 'Post your game highlights',
    icon: Instagram,
    xp: 100,
  },
];



const BoostModal: React.FC<BoostModalProps> = ({ onClose, onWatchAd, onShare }) => {
  const { accessToken } = useAuth();
  const [isMobile, setIsMobile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [copied, setCopied] = useState<boolean>(false)

  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);


  const referralCode = userProfile?.referral_code;
  const shareUrl = referralCode ? `${window.location.origin}/auth?ref=${referralCode}` : '';

const handleShare = (platform: string) => {
  if (!shareUrl) return;

  // Copy referral link to clipboard
  navigator.clipboard.writeText(shareUrl).then(() => {
    setCopied(true);
    CustomToast('Link copied to clipboard!', 'success');
  });

  let shareLink = '';
  const message = `Join me on Rise & Hustle! Use my referral link: ${shareUrl}`;

  switch (platform) {
    case 'whatsapp':
      shareLink = `https://wa.me/?text=${encodeURIComponent(message)}`;
      break;
    case 'telegram':
      shareLink = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent('Join me on Rise & Hustle!')}`;
      break;
    case 'twitter':
      shareLink = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
      break;
    case 'facebook':
      shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
      break;
    case 'instagram':
      shareLink=`https://www.instagram.com/`
      // Instagram doesn't support direct URL sharing – consider fallback message or image-based sharing
      // CustomToast('Instagram sharing must be done manually', 'info');
      return;
  }

  if (shareLink) {
    window.open(shareLink, '_blank');
  }
};

  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;
  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        {/* <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl w-full max-w-md"> */}
        <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
          <h2 className="text-[20px] font-bold flex items-center gap-2 font-[Anton]">
            Boost Your Progress
          </h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-4">
          {BOOST_OPTIONS.map(option => {
            const IconComponent = option.icon;
            return (
              <button
                key={option.id}
                onClick={async () => {
                  const isAd = option.id === 'watch_ad';
                  const isShare = option.id.startsWith('share_');

                  const rewardData = {
                    user_id: userProfile?.user_id,
                    game: isAd ? 'rewarded_ad' : 'share',
                    position: 1,
                    reward_type: 'P',
                    reward_amount: option.xp,
                    source: 'web_app',
                    transaction_id: '',
                    wallet_id: '',
                  };

                  if (isAd) {
                    onWatchAd();
                  } else if (isShare) {
                    handleShare(option.id.replace('share_', ''));
                    onShare(option.id);
                  }

                  await postRewardClaim(rewardData);
                  onClose();
                }}

                className="w-full bg-[#131313] p-4 rounded-xl  transition-colors flex items-center gap-4"
              >
                <div className="bg-white/10 p-3 rounded-lg">
                  <IconComponent size={24} className="text-[#ED0CFF]" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-[14px] font-[Anton] font-normal">{option.title}</h3>
                  <p className="text-[12px] text-white/60 w-[129px] h-[32px]">{option.description}</p>
                </div>
                <div className="flex items-center gap-1 text-white font-[Anton]">
                  <span>+ {option.xp}</span>
                  <span className="text-[14px] uppercase">Bucks</span>
                </div>
              </button>
            );
          })}
        </div>
      </div>
      {/* </div> */}
    </div>
  );
};

export default BoostModal;