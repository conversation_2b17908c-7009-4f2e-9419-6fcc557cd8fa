import React, { useState, useEffect, useRef, FC } from 'react';
import { History, ChevronLeft } from 'lucide-react';
import { useMutation, useInfiniteQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import winSound from '../Sounds/CryptoKing/win_sound.m4a';
import BackGroundSound from '../Sounds/StreetKing2/Street_king_2_background.mp3';
import { useAuth } from '../auth/AuthContext';
import { useSocketContext } from '../context/socketProvider';
import { useCoins } from '../hooks/useCoinsQuery';
import { CustomToast } from '../utils/validations/customeToast';
import {
  getStreetKingsHistory,
  placeBet,
  cashOut as cashOutService,
} from '../api/streetKingsService';
import SoundManager from '../components/soundManager/SoundManager';
import Transactions from '../components/Transactions';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from '../components/GameLoadingScreen/GameLoadingScreen';
import { COLORS } from '../constant/theming';
import { PzButton } from '../components/Shared/PzButton';
import { PzErrorPopup } from '../components/Shared/PzErrorPopup';
import { useNavigate } from 'react-router-dom';
import { DEFAULT_BET } from '../constant/numbers';
/**
 * Formats a numeric value into a localized string with commas.
 * @param {number | string} value - The value to format.
 * @returns {string} The formatted number string.
 */
const formatCoins = (value: number | string): string =>
  Number(value).toLocaleString();

/**
 * Formats a multiplier value to two decimal places.
 * @param {number} value - The multiplier value.
 * @returns {string} The formatted multiplier string.
 */
const formatMultiplier = (value: number): string => value.toFixed(2);

// Constants for simulation
const BASE_MULTIPLIER = 0.0;
const MULTIPLIER_INCREASE = 0.02;

type CrashGame2Props = {
  onWin: (reward: { type: string; amount?: number }) => void;
  onPurchase: (cost: number) => void;
  balance: number;
};

/**
 * CrashGame2 component renders the Crash game UI with a canvas graph,
 * bet controls, and infinite-scroll game history.
 * @component
 */
const CrashGame2: FC<CrashGame2Props> = ({ onWin, onPurchase, balance }) => {
  const { accessToken } = useAuth();
  const { socketState, isConnected } = useSocketContext();
  const { coins, addCoins, refetchCoins } = useCoins();
  const navigate = useNavigate();
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [didWin, setDidWin] = useState(false);
  // Refs
  const gameLoopRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const crashPointRef = useRef(BASE_MULTIPLIER);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const startTimeRef = useRef(0);

  // Local state
  const [showCrashMessage, setShowCrashMessage] = useState(false);
  const [hasBet, setHasBet] = useState(false);
  const [hasCashedOut, setHasCashedOut] = useState(false);
  const [showCashoutAnimation, setShowCashoutAnimation] = useState(false);
  const [isCrashed, setIsCrashed] = useState(false);
  const [currentBet, setCurrentBet] = useState<number | string>(100);
  const [betAmount, setBetAmount] = useState(DEFAULT_BET);
  const [multiplierValue, setMultiplierValue] = useState(BASE_MULTIPLIER);
  const [currentProfit, setCurrentProfit] = useState(0);
  const [roundId, setRoundId] = useState('');
  const [showFundsErrorPopup, setShowFundsErrorPopup] = useState(false);

  // Betting limits
  const MIN_BET = 1;
  const MAX_BET = Math.min(coins, 1000);

  /**
   * Scroll to top on mount.
   */
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  /**
   * Draws the multiplier graph and the moving car on the canvas.
   */
  const drawGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Setup padding and scales
    const padding = { left: 60, right: 40, top: 40, bottom: 40 };
    const graphWidth = canvas.width - padding.left - padding.right;
    const graphHeight = canvas.height - padding.top - padding.bottom;
    const maxMultiplier = Math.max(Math.ceil(multiplierValue * 1.5), 5);
    const yScale = graphHeight / maxMultiplier;
    const elapsedTime = hasBet ? (Date.now() - startTimeRef.current) / 1000 : 0;
    const maxTime = Math.max(
      isCrashed
        ? (crashPointRef.current - 1) / (MULTIPLIER_INCREASE * 20)
        : elapsedTime,
      5
    );
    const xScale = graphWidth / maxTime;

    // Draw grid lines and labels
    ctx.strokeStyle = 'rgba(255,255,255,0.1)';
    ctx.lineWidth = 1;
    ctx.fillStyle = 'rgba(255,255,255,0.6)';
    ctx.font = '12px sans-serif';

    // Y-axis lines
    for (let i = 0; i <= 10; i++) {
      const value = (maxMultiplier * i) / 10;
      const y = canvas.height - padding.bottom - value * yScale;
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(canvas.width - padding.right, y);
      ctx.stroke();
      ctx.fillText(`${value.toFixed(1)}x`, padding.left - 10, y);
    }

    // X-axis lines
    ctx.textAlign = 'center';
    for (let i = 0; i <= 5; i++) {
      const t = (maxTime * i) / 5;
      const x = padding.left + t * xScale;
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, canvas.height - padding.bottom);
      ctx.stroke();
      ctx.fillText(`${t.toFixed(1)}s`, x, canvas.height - padding.bottom + 10);
    }

    // Draw multiplier curve
    if (isConnected) {
      ctx.beginPath();
      ctx.strokeStyle = isCrashed ? '#ef4444' : COLORS.primary;
      ctx.lineWidth = 3;

      const points = [];
      const endT = isCrashed ? maxTime : elapsedTime;
      for (let t = 0; t <= endT; t += 0.01) {
        const m = 1 + MULTIPLIER_INCREASE * 20 * t;
        const x = padding.left + t * xScale;
        const y = canvas.height - padding.bottom - (m - 1) * yScale;
        points.push({ x, y });
      }

      // Fill gradient under curve
      if (points.length) {
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(
          0,
          isCrashed ? 'rgba(239,68,68,0.2)' : 'rgba(251,191,36,0.2)'
        );
        gradient.addColorStop(1, 'rgba(0,0,0,0)');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.moveTo(points[0].x, canvas.height - padding.bottom);
        const lastPoint = points.at(-1);
        if (lastPoint) {
          ctx.lineTo(lastPoint.x, canvas.height - padding.bottom);
        }
        ctx.closePath();
        ctx.fill();

        // Stroke curve
        ctx.beginPath();

        points.forEach(({ x, y }, index) =>
          index ? ctx.lineTo(x, y) : ctx.moveTo(x, y)
        );
        ctx.stroke();
      }

      if (hasBet) {
        const last = points.at(-1);
        const prev = points.at(-2) || last;
        if (last) {
          const angle = Math.atan2(
            last.y - (prev?.y ?? last.y),
            last.x - (prev?.x ?? last.x)
          );
          ctx.save();
          ctx.translate(last.x, last.y);
          ctx.rotate(angle);
          const size = 24;
          ctx.fillStyle = isCrashed ? '#ef4444' : COLORS.primary;
          ctx.roundRect(-size / 2, -size / 2, size, size / 2, 4);
          ctx.fill();
          ctx.roundRect(-size / 3, -size / 1.5, size / 1.5, size / 3, 2);
          ctx.fill();
          ctx.arc(-size / 3, 0, size / 6, 0, Math.PI * 2);
          ctx.arc(size / 3, 0, size / 6, 0, Math.PI * 2);
          ctx.fill();
          ctx.restore();
        }
      }

      // Draw central multiplier text or crash message
      ctx.save();
      ctx.font = 'bold 48px sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      if (isCrashed) {
        ctx.fillStyle = showCrashMessage ? '#ef4444' : '#ffffff';
        ctx.shadowColor = showCrashMessage ? '#ef4444' : 'transparent';
        ctx.shadowBlur = showCrashMessage ? 10 : 0;
        ctx.fillText(
          showCrashMessage
            ? `${formatMultiplier(
                Number(socketState?.data?.round.crash_point)
              )}x`
            : '0.00x',
          canvas.width / 2,
          canvas.height / 2
        );
      } else if (hasCashedOut) {
        ctx.fillStyle = '#10b981';
        ctx.shadowColor = '#10b981';
        ctx.shadowBlur = 10;
        ctx.fillText(
          formatMultiplier(multiplierValue),
          canvas.width / 2,
          canvas.height / 2
        );
      } else {
        ctx.fillStyle = hasBet ? COLORS.primary : '#ffffff';
        ctx.fillText(
          hasBet ? formatMultiplier(multiplierValue) : '0.00x',
          canvas.width / 2,
          canvas.height / 2
        );
      }
      ctx.restore();
    }
  };
  /**
   * Initializes canvas dimensions and draws initial graph.
   */
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      setIsCrashed(false);
      canvas.width = canvas.offsetWidth * window.devicePixelRatio;
      canvas.height = canvas.offsetHeight * window.devicePixelRatio;
      drawGraph();
    }
  }, []);

  useEffect(() => {
    let frameId: number | undefined = undefined;

    /**
     * Animation loop function for smooth chart updates
     */
    const animate = () => {
      drawGraph();
      frameId = requestAnimationFrame(animate);
    };

    if (hasBet) {
      // Cancel any previous animation before starting a new one
      if (frameId) {
        cancelAnimationFrame(frameId);
      }
      frameId = requestAnimationFrame(animate);
    } else {
      setMultiplierValue(BASE_MULTIPLIER);
      // When not playing, just ensure the graph is drawn once
      if (canvasRef.current) {
        drawGraph();
      }
    }

    // Cleanup function
    return () => {
      if (frameId) {
        cancelAnimationFrame(frameId); // Cleanup on unmount or when dependencies change
      }
    };
  }, [hasBet, multiplierValue]);

  /**
   * Handles bet placement via API and starts simulation or socket updates.
   */
  const placeBetMutation = useMutation({
    mutationFn: async () => {
      if (!accessToken) throw new Error('token is missing');
      const res = await placeBet(
        { bet_amount: betAmount, version: 'v2' },
        accessToken
      );
      if (res.data.id) setRoundId(res.data.id);
      onPurchase(Number(betAmount));
      return res;
    },
    onSuccess: () => {
      CustomToast('success', 'Bet placed! Good luck!');
      setHasBet(true);
      setHasCashedOut(false);
      startTimeRef.current = Date.now();
      startGameLoop();
      refetchCoins();
    },
    onError: (e) => CustomToast('error', e.message || 'Failed to place bet'),
  });

  /**
   * Handles cash out via API and stops simulation.
   */
  const cashOutMutation = useMutation({
    mutationFn: () => {
      if (!roundId) throw new Error('Round ID is missing');
      if (!accessToken) throw new Error('Token is missing');
      return cashOutService({ round_id: roundId }, accessToken);
    },
    onSuccess: (res) => {
      const profit = parseFloat(res.data.won_amount);
      setCurrentProfit(profit);
      addCoins(profit);
      CustomToast('success', `Cashed out +${formatCoins(profit)} coins`);
      setDidWin(true);
      setHasCashedOut(true);
      setShowCashoutAnimation(true);
      setHasBet(false);
      onWin({ type: 'coins' });

      if (gameLoopRef.current !== null) {
        clearInterval(gameLoopRef.current);
      }

      setTimeout(() => {
        setDidWin(false);
        refetchCoins();
        setHasCashedOut(false);
        setShowCashoutAnimation(false);
        setMultiplierValue(Number(socketState?.data?.multiplayer));
        refetchHistory();
      }, 2000);
    },
  });

  /**
   * Resets state on server crash event.
   */
  useEffect(() => {
    if (socketState?.type === 'crash') {
      setIsCrashed(true);
      setShowCrashMessage(true);
      setHasBet(false);
      refetchHistory();
      const t = setTimeout(() => {
        setShowCrashMessage(false);
        setIsCrashed(false);
        setHasCashedOut(false);
        setMultiplierValue(BASE_MULTIPLIER);
        setShowCashoutAnimation(false);
      }, 3000);
      return () => clearTimeout(t);
    }
  }, [socketState]);

  /**
   * Disconnects simulation when no active bet.
   */
  useEffect(() => {
    if (!hasBet) {
      const t = setTimeout(() => {
        setMultiplierValue(BASE_MULTIPLIER);
        setShowCashoutAnimation(false);
      }, 1000);
      return () => clearTimeout(t);
    }
  }, [hasBet]);

  /**
   * Handler for bet amount input change.
   * @param {React.ChangeEvent<HTMLInputElement>} e
   */
  const handleBetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = parseInt(e.target.value, 10) || 0;
    setBetAmount(val);
    setCurrentBet(val || '');
  };

  /** Halves the current bet. */
  const halfBet = () =>
    setCurrentBet((prev) => Math.max(MIN_BET, Math.floor(Number(prev) / 2)));

  /** Doubles the current bet up to MAX_BET. */
  const doubleBet = () =>
    setCurrentBet((prev) => Math.min(Number(prev) * 2, MAX_BET));

  /**
   * Starts local game loop simulation, updating multiplierValue.
   */
  const startGameLoop = () => {
    if (gameLoopRef.current) clearInterval(gameLoopRef.current);
    setIsCrashed(false);

    // Redraw the graph with clean state
    if (canvasRef.current) {
      drawGraph();
    }
  };

  /**
   * Syncs multiplierValue with server when connected.
   */
  useEffect(() => {
    if (isConnected && socketState?.data?.multiplayer) {
      setMultiplierValue(Number(socketState.data.multiplayer));
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    }
  }, [isConnected, socketState]);

  // Fetch game history
  const {
    data: historyPages,
    hasNextPage,
    refetch: refetchHistory,
    isLoading: isHistoryLoading,
  } = useInfiniteQuery({
    queryKey: ['history'],
    queryFn: ({ pageParam = 1 }: { pageParam: number }) => {
      if (!accessToken) throw new Error('Access token is missing');
      return getStreetKingsHistory(
        { page: pageParam, per_page: 10, version: 'v2' },
        accessToken
      );
    },
    getNextPageParam: (lastPage, allPages) => {
      // Determine if more pages exist
      if (!lastPage?.total_pages || allPages.length >= lastPage.total_pages)
        return undefined;
      return allPages.length + 1;
    },
    initialPageParam: 1,
    staleTime: 0,
    refetchOnWindowFocus: true,
    enabled: !!accessToken,
  });
  const onClose = () => {
    setShowTransactionModal(false);
  };

  const { isGameDataLoading, visibility } = useGameDataLoading(
    !isHistoryLoading,
    isConnected,
    isFinite(coins)
  );

  const goToHomePage = () => navigate('/');

  const onPlaceBetClick = () => {
    coins < betAmount
      ? setShowFundsErrorPopup(true)
      : placeBetMutation.mutate();
  };

  return (
    <>
      {isGameDataLoading && <GameLoadingScreen />}
      <div
        className="min-h-screen text-text-primary relative"
        style={{
          visibility,
        }}
      >
        <SoundManager
          sounds={{
            background: BackGroundSound,
            win: winSound,
          }}
          // loop the background while the game is running:
          backgroundKey={hasBet && !didWin ? 'background' : null}
          // play one of these exactly once when it changes:
          playKey={didWin ? 'win' : undefined}
          volumes={{
            background: 0.4,
            win: 1.0,
          }}
        />
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

        <div className="max-w-4xl mx-auto relative">
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={goToHomePage}
              className="text-white/60 hover:text-white transition-colors"
            >
              <ChevronLeft size={24} />
            </button>
            <h1 className="text-2xl font-bold">Street King 2</h1>
          </div>

          <div className="space-y-6">
            <div className="space-y-6">
              <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6 text-center relative overflow-hidden">
                <canvas
                  ref={canvasRef}
                  width={800}
                  height={400}
                  className="w-full h-[300px]"
                />

                {showCashoutAnimation && (
                  <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse">
                    <div className="text-3xl font-bold text-green-400">
                      +
                      {formatCoins(
                        currentProfit > 0
                          ? currentProfit
                          : Math.floor(Number(currentBet) * multiplierValue)
                      )}{' '}
                      coins
                    </div>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <PzButton
                  text="Place Bet"
                  onClick={onPlaceBetClick}
                  isDisabled={(hasBet && betAmount > coins) || betAmount <= 0}
                />
                <PzButton
                  text="Cash Out"
                  isDisabled={
                    (hasBet && betAmount > coins) || betAmount <= 0 || !hasBet
                  }
                  onClick={() => cashOutMutation.mutate()}
                  className={`py-3 rounded-lg font-medium ${
                    !hasBet
                      ? 'bg-white/10 text-white/40 cursor-not-allowed'
                      : 'bg-green-500 hover:bg-green-400 text-black'
                  } disabled:opacity-50`}
                />
              </div>

              <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Bet Amount
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={currentBet}
                        onChange={handleBetChange}
                        disabled={hasBet}
                        className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-[${COLORS.primary}] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]`}
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                        <button
                          onClick={halfBet}
                          disabled={hasBet}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors"
                        >
                          ½
                        </button>
                        <button
                          onClick={doubleBet}
                          disabled={hasBet}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors"
                        >
                          2×
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Available Balance
                    </label>
                    <div className="flex items-center gap-2 px-3 py-2 bg-white/5 border border-white/10 rounded-lg">
                      {formatCoins(Math.floor(balance))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              <h2 className="text-lg font-[Anton] mb-4 flex items-center gap-2">
                <History
                  size={20}
                  color={COLORS.primary}
                  className="text-yellow-400"
                />
                Game History
              </h2>

              <div className="space-y-4">
                {/* Then show server history */}
                {historyPages?.pages &&
                  historyPages?.pages
                    ?.flatMap((page) => (page as any)?.data || [])
                    .map((game, index) => {
                      const lost = game.won_status === 'LOSE';
                      const profit = parseFloat(game.won_amount);

                      return (
                        <div
                          key={index}
                          className={`p-4 rounded-lg font-[Poppins] ${
                            lost ? 'bg-red-500/10' : 'bg-green-500/10'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span
                              className={
                                lost ? 'text-red-400' : 'text-green-400'
                              }
                            >
                              {lost
                                ? `${parseFloat(game.crash_point).toFixed(1)}x`
                                : `${parseFloat(game.cashout_point).toFixed(
                                    1
                                  )}x`}
                            </span>
                            {lost ? (
                              <span className={'text-red-400'}>
                                {'-'}
                                {formatCoins(game?.bet_amount)}
                              </span>
                            ) : (
                              <span className={'text-green-400'}>
                                {profit > 0 ? '+' : ''}
                                {formatCoins(profit)}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center justify-between text-sm text-white/60">
                            <span>Bet: {formatCoins(game.bet_amount)}</span>
                            <span>
                              {dayjs(game.timestamp).format('hh:mm A')}
                            </span>
                          </div>
                        </div>
                      );
                    })}

                {/* Load More Button */}
                {hasNextPage && (
                  <PzButton
                    text="View All"
                    onClick={() => {
                      setShowTransactionModal(true);
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        {showTransactionModal && <Transactions onClose={onClose} />}
        {showFundsErrorPopup && (
          <PzErrorPopup
            setShowFundsErrorPopup={setShowFundsErrorPopup}
            cost={Number(currentBet)}
          />
        )}
      </div>
    </>
  );
};
export default React.memo(CrashGame2);
